<?php
use system\router;

header("Cache-Control: no-store, no-cache, must-revalidate"); // HTTP/1.1
header("Cache-Control: post-check=0, pre-check=0", false);
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Date in the past
header("Pragma: no-cache"); // HTTP/1.0
header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
$path['fs_app_root'] = __DIR__ . '/';
$schema = require_once $path['fs_app_root'] . 'system/config/path_schema.php';
$path['fs_system'] = "{$path['fs_app_root']}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);

define('INPUT_PARAMS',$input_params);
define('DEBUG_MODE', true);
define('API_RUN', false);
require_once $path['fs_system'] . '/classes/startup_sequence.class.php';
require_once $path['fs_system'] . '/functions/functions.php';

startup_sequence::start($path,$schema);


print_rr("starting route");
echo router::route();
include('resources/components/modal.php');